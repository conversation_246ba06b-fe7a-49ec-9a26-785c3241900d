package dashboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	_dashboard "github.com/dsoplabs/dinbora-backend/internal/repository/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
)

// Service interface defines the dashboard service operations
type Service interface {
	// FinancialMap operations
	FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error)

	// IncomeSource operations
	CreateIncomeSource(ctx context.Context, userID string, name string, monthlyAmount monetary.Amount) (*dashboard.IncomeSource, error)
	FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error)
	UpdateIncomeSource(ctx context.Context, id string, name string, monthlyAmount monetary.Amount) error
	DeleteIncomeSource(ctx context.Context, id string) error

	// StrategicFund operations
	FindStrategicFund(ctx context.Context, userID string) (*dashboard.StrategicFund, error)
	UpdateStrategicFund(ctx context.Context, userID string, currentValue monetary.Amount) error
	UpdateStrategicFundGoal(ctx context.Context, userID string, goalValue monetary.Amount) error

	// Investment operations
	CreateInvestment(ctx context.Context, userID string, name string, currentValue monetary.Amount) (*dashboard.Investment, error)
	FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error)
	UpdateInvestment(ctx context.Context, userID string, id string, name string, currentValue monetary.Amount) error
	DeleteInvestment(ctx context.Context, userID string, id string) error

	// Asset operations
	CreateAsset(ctx context.Context, userID string, description string, value monetary.Amount) (*dashboard.Asset, error)
	FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error)
	UpdateAsset(ctx context.Context, userID string, id string, description string, value monetary.Amount) error
	DeleteAsset(ctx context.Context, userID string, id string) error

	// Snapshot operations
	CreateMonthlySnapshot(ctx context.Context, userID string, snapshotDate time.Time, creationDate time.Time) error
	FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error)
	FindLatestNetWorthSnapshot(ctx context.Context) (time.Time, error)

	// Financial Stress operations
	FindFinancialStress(ctx context.Context, userID string, period string) (*dashboard.FinancialStress, error)
	FindFixedExpenses(ctx context.Context, userID string, period string) (*dashboard.ExpenseCategory, error)
	FindVariableExpenses(ctx context.Context, userID string, period string) (*dashboard.ExpenseCategory, error)
	FindDebtExpenses(ctx context.Context, userID string, period string) (*dashboard.ExpenseCategory, error)
	FindExpenseAnalysisDetails(ctx context.Context, userID string, categoryID string, period string) (*dashboard.CategoryBreakdown, error)
}

type service struct {
	Repository            _dashboard.Repository
	FinancialSheetService financialsheet.Service
}

// New creates a new dashboard service
func New(repository _dashboard.Repository, financialSheetService financialsheet.Service) Service {
	return &service{
		Repository:            repository,
		FinancialSheetService: financialSheetService,
	}
}
