package dashboard

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	// Unified collection
	financialMapCollection *mongo.Collection
}

// New creates a new dashboard repository with MongoDB implementation
func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		// Unified collection
		financialMapCollection: db.Collection(repository.DASHBOARD_FINANCIAL_MAP_COLLECTION),
	}

	// Create indexes
	repo.createIndexes()

	return repo
}

// isNotFoundError checks if an error is a not found error
func isNotFoundError(err error) bool {
	if err == nil {
		return false
	}
	// Check if it's our custom not found error
	if domainErr, ok := err.(*errors.DomainError); ok {
		return domainErr.Kind() == errors.NotFound
	}
	return false
}

// createIndexes creates necessary indexes for dashboard collections
func (m *mongoDB) createIndexes() {
	ctx := context.Background()

	// Unified FinancialMap collection indexes
	_, err := m.financialMapCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys:    bson.D{{Key: "userID", Value: 1}},
		Options: options.Index().SetUnique(true),
	})
	if err != nil {
		log.Printf("Warning: failed to create unique index on financial map userID field: %v", err)
	}

}
