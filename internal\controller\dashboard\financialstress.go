package dashboard

import (
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/labstack/echo/v4"
)

// FindFinancialStress handles GET /dashboard/financialstress?period=30d
func (dc *controller) FindFinancialStress() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get user ID from context (set by auth middleware)
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get period parameter with default value
		period := c.QueryParam("period")
		if period == "" {
			period = "30d" // Default to 30 days
		}

		// Validate period parameter
		if !isValidPeriod(period) {
			return errors.New(errors.Controller, "invalid period parameter", errors.BadRequest, nil)
		}

		// Get financial stress analysis from service
		financialStress, err := dc.Service.FindFinancialStress(ctx, userToken.Uid, period)
		if err != nil {
			return err
		}

		// Build the DTO
		categoriesDTO := []*ExpenseAnalysisCategoriesDTO{}
		for _, category := range financialStress.ExpenseAnalysis.Categories {
			categoriesDTO = append(categoriesDTO, &ExpenseAnalysisCategoriesDTO{
				CategoryIdentifier: category.CategoryIdentifier,
				CategoryName:       category.CategoryName,
				Amount:             category.Amount,
				Percentage:         category.Percentage,
			})
		}

		topIncreases := []*dashboard.CategoryVariation{}
		for _, increase := range financialStress.SpendingVariation.TopIncreases {
			topIncreases = append(topIncreases, &dashboard.CategoryVariation{
				CategoryIdentifier: increase.CategoryIdentifier,
				CategoryName:       increase.CategoryName,
				Amount:             increase.Amount,
				Percentage:         increase.Percentage,
				Direction:          increase.Direction,
			})
		}

		topReductions := []*dashboard.CategoryVariation{}
		for _, reduction := range financialStress.SpendingVariation.TopReductions {
			topReductions = append(topReductions, &dashboard.CategoryVariation{
				CategoryIdentifier: reduction.CategoryIdentifier,
				CategoryName:       reduction.CategoryName,
				Amount:             reduction.Amount,
				Percentage:         reduction.Percentage,
				Direction:          reduction.Direction,
			})
		}

		financialStressDTO := &FinancialStressDTO{
			Score:              financialStress.StressScore,
			CommitmentAnalysis: financialStress.CommitmentAnalysis,
			ExpenseAnalysis:    categoriesDTO,
			PaymentMethods:     financialStress.PaymentMethods,
			TopIncreases:       topIncreases,
			TopReductions:      topReductions,
			Period:             financialStress.Period,
			AnalysisDate:       financialStress.AnalysisDate.Format("2006-01-02"),
		}

		return c.JSON(http.StatusOK, financialStressDTO)
	}
}

// FindFixedExpenses handles GET /dashboard/financialstress/expenses/fixed?period=30d
func (dc *controller) FindFixedExpenses() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get user ID from context (set by auth middleware)
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get period parameter with default value
		period := c.QueryParam("period")
		if period == "" {
			period = "30d" // Default to 30 days
		}

		// Validate period parameter
		if !isValidPeriod(period) {
			return errors.New(errors.Controller, "invalid period parameter", errors.BadRequest, nil)
		}

		// Get fixed expenses analysis from service
		fixedExpenses, err := dc.Service.FindFixedExpenses(ctx, userToken.Uid, period)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, fixedExpenses)
	}
}

// FindVariableExpenses handles GET /dashboard/financialstress/expenses/variable?period=30d
func (dc *controller) FindVariableExpenses() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get user ID from context (set by auth middleware)
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get period parameter with default value
		period := c.QueryParam("period")
		if period == "" {
			period = "30d" // Default to 30 days
		}

		// Validate period parameter
		if !isValidPeriod(period) {
			return errors.New(errors.Controller, "invalid period parameter", errors.BadRequest, nil)
		}

		// Get variable expenses analysis from service
		variableExpenses, err := dc.Service.FindVariableExpenses(ctx, userToken.Uid, period)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, variableExpenses)
	}
}

// FindDebtExpenses handles GET /dashboard/financialstress/expenses/debt?period=30d
func (dc *controller) FindDebtExpenses() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get user ID from context (set by auth middleware)
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get period parameter with default value
		period := c.QueryParam("period")
		if period == "" {
			period = "30d" // Default to 30 days
		}

		// Validate period parameter
		if !isValidPeriod(period) {
			return errors.New(errors.Controller, "invalid period parameter", errors.BadRequest, nil)
		}

		// Get debt expenses analysis from service
		debtExpenses, err := dc.Service.FindDebtExpenses(ctx, userToken.Uid, period)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, debtExpenses)
	}
}

// FindExpenseAnalysisDetails handles GET /dashboard/financialstress/expenses/analysis/:identifier?period=30d
func (dc *controller) FindExpenseAnalysisDetails() echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get user ID from context (set by auth middleware)
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get category ID from path parameter
		categoryID := c.Param("identifier")
		if categoryID == "" {
			return errors.New(errors.Controller, "category ID is required", errors.BadRequest, nil)
		}

		// Get period parameter with default value
		period := c.QueryParam("period")
		if period == "" {
			period = "30d" // Default to 30 days
		}

		// Validate period parameter
		if !isValidPeriod(period) {
			return errors.New(errors.Controller, "invalid period parameter", errors.BadRequest, nil)
		}

		// Get category details from service
		categoryDetails, err := dc.Service.FindExpenseAnalysisDetails(ctx, userToken.Uid, categoryID, period)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, categoryDetails)
	}
}

// Helper function to validate period parameter
func isValidPeriod(period string) bool {
	validPeriods := map[string]bool{
		"7d":  true,
		"30d": true,
		"90d": true,
		"1y":  true,
	}

	return validPeriods[period]
}
