package dashboard

import (
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

type FinancialStressDTO struct {
	Score              *dashboard.StressScore            `json:"stressScore"`
	CommitmentAnalysis *dashboard.CommitmentAnalysis     `json:"commitmentAnalysis"`
	ExpenseAnalysis    []*ExpenseAnalysisCategoriesDTO   `json:"expenseAnalysis"`
	PaymentMethods     []*dashboard.PaymentMethodSummary `json:"paymentMethods"`
	TopIncreases       []*dashboard.CategoryVariation    `json:"topIncreases"`
	TopReductions      []*dashboard.CategoryVariation    `json:"topReductions"`
	Period             string                            `json:"period"`
	AnalysisDate       string                            `json:"analysisDate"`
}

type ExpenseAnalysisCategoriesDTO struct {
	CategoryIdentifier string          `json:"categoryIdentifier"`
	CategoryName       string          `json:"categoryName"`
	Amount             monetary.Amount `json:"amount"`
	Percentage         float64         `json:"percentage"`
}
