package dashboard

import (
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// FinancialMapCard represents a card in the financial map dashboard
type FinancialMapCard struct {
	ID               string                 `json:"id"`
	MonthlyIncome    MonthlyIncomeCard      `json:"monthlyIncome" bson:"monthlyIncome"`
	StrategicFund    StrategicFundCard      `json:"strategicFund" bson:"strategicFund"`
	TotalInvestments InvestmentCard         `json:"totalInvestments" bson:"totalInvestments"`
	TotalAssets      AssetCard              `json:"totalAssets" bson:"totalAssets"`
	NetWorthHistory  []*NetWorthHistoryCard `json:"netWorthHistory" bson:"netWorthHistory"`
}

type MonthlyIncomeCard struct {
	Value           monetary.Amount `json:"value" bson:"value"`
	SourcesOfIncome byte            `json:"sourcesOfIncome" bson:"sourcesOfIncome"`
	Icon            string          `json:"icon"`
	BackgroundColor string          `json:"backgroundColor"`
}

type StrategicFundCard struct {
	CurrentValue    monetary.Amount `json:"currentValue" bson:"currentValue"`
	GoalValue       monetary.Amount `json:"goalValue" bson:"goalValue"`
	Icon            string          `json:"icon"`
	BackgroundColor string          `json:"backgroundColor"`
}

type InvestmentCard struct {
	Value             monetary.Amount `json:"value" bson:"value"`
	ActiveInvestments byte            `json:"activeInvestments" bson:"activeInvestments"`
	Icon              string          `json:"icon"`
	BackgroundColor   string          `json:"backgroundColor"`
}

type AssetCard struct {
	Value            monetary.Amount `json:"value" bson:"value"`
	RegisteredAssets byte            `json:"registeredAssets" bson:"registeredAssets"`
	Icon             string          `json:"icon"`
	BackgroundColor  string          `json:"backgroundColor"`
}

type NetWorthHistoryCard struct {
	Stacks []*NetWorthStack `json:"stacks" bson:"stacks"`
	Label  string           `json:"label" bson:"label"`
}

type NetWorthStack struct {
	Value monetary.Amount `json:"value" bson:"value"`
	Color string          `json:"color" bson:"color"`
}

// IncomeSource request templates
type CreateIncomeSourceRequest struct {
	Name          string          `json:"name" validate:"required"`
	MonthlyAmount monetary.Amount `json:"monthlyAmount" validate:"required,min=1"`
}

type UpdateIncomeSourceRequest struct {
	Name          string          `json:"name" validate:"required"`
	MonthlyAmount monetary.Amount `json:"monthlyAmount" validate:"required,min=1"`
}

// StrategicFund request templates
type UpdateStrategicFundRequest struct {
	CurrentValue monetary.Amount `json:"currentValue" validate:"required,min=0"`
}

type UpdateStrategicFundGoalRequest struct {
	GoalValue monetary.Amount `json:"goalValue" validate:"required,min=1"`
}

// Investment request templates
type CreateInvestmentRequest struct {
	Name         string          `json:"name" validate:"required"`
	CurrentValue monetary.Amount `json:"currentValue" validate:"required,min=0"`
}

type UpdateInvestmentRequest struct {
	Name         string          `json:"name" validate:"required"`
	CurrentValue monetary.Amount `json:"currentValue" validate:"required,min=0"`
}

// Asset request templates
type CreateAssetRequest struct {
	Name         string          `json:"name" validate:"required"`
	CurrentValue monetary.Amount `json:"currentValue" validate:"required,min=0"`
}

type UpdateAssetRequest struct {
	Name         string          `json:"name" validate:"required"`
	CurrentValue monetary.Amount `json:"currentValue" validate:"required,min=0"`
}
