package dashboard

import (
	"net/http"
	"strconv"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/labstack/echo/v4"
)

// Financial Map
func (dc *controller) FindFinancialMap() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		financialMap, err := dc.Service.FindFinancialMap(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, financialMap)
	}
}

// Cards
func (dc *controller) FindFinancialMapCard() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		financialMap, err := dc.Service.FindFinancialMap(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Build card
		card := &FinancialMapCard{
			ID: financialMap.ID,
			MonthlyIncome: MonthlyIncomeCard{
				Value:           financialMap.MonthlyIncome,
				SourcesOfIncome: byte(len(financialMap.IncomeSources)),
				Icon:            "https://images.dinbora.com.br/dashboard-usuario/renda-mensal.png",
				BackgroundColor: "#D3FFDA",
			},
			StrategicFund: StrategicFundCard{
				CurrentValue:    financialMap.StrategicFund.CurrentValue,
				GoalValue:       financialMap.StrategicFund.GoalValue,
				Icon:            "https://images.dinbora.com.br/dashboard-usuario/reserva-estrategica.png",
				BackgroundColor: "#FEF3C7",
			},
			TotalInvestments: InvestmentCard{
				Value:             financialMap.TotalInvestments,
				ActiveInvestments: byte(len(financialMap.Investments)),
				Icon:              "https://images.dinbora.com.br/dashboard-usuario/investimentos.png",
				BackgroundColor:   "#F6E4FF",
			},
			TotalAssets: AssetCard{
				Value:            financialMap.TotalAssets,
				RegisteredAssets: byte(len(financialMap.Assets)),
				Icon:             "https://images.dinbora.com.br/dashboard-usuario/bens.png",
				BackgroundColor:  "#D0EAFF",
			},
			NetWorthHistory: dc.buildNetWorthHistoryCard(financialMap.NetWorthHistory),
		}

		return c.JSON(http.StatusOK, card)
	}
}

// IncomeSource operations
func (dc *controller) CreateIncomeSource() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var request CreateIncomeSourceRequest
		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		incomeSource, err := dc.Service.CreateIncomeSource(ctx, userToken.Uid, request.Name, request.MonthlyAmount)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, incomeSource)
	}
}

func (dc *controller) FindIncomeSources() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		incomeSources, err := dc.Service.FindIncomeSources(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, incomeSources)
	}
}

func (dc *controller) UpdateIncomeSource() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := c.Param("id")

		var request UpdateIncomeSourceRequest
		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		err := dc.Service.UpdateIncomeSource(ctx, id, request.Name, request.MonthlyAmount)
		if err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

func (dc *controller) DeleteIncomeSource() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		id := c.Param("id")

		err := dc.Service.DeleteIncomeSource(ctx, id)
		if err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// StrategicFund operations
func (dc *controller) FindStrategicFund() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		strategicFund, err := dc.Service.FindStrategicFund(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, strategicFund)
	}
}

func (dc *controller) UpdateStrategicFund() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var request UpdateStrategicFundRequest
		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		err = dc.Service.UpdateStrategicFund(ctx, userToken.Uid, request.CurrentValue)
		if err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

func (dc *controller) UpdateStrategicFundGoal() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var request UpdateStrategicFundGoalRequest
		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		err = dc.Service.UpdateStrategicFundGoal(ctx, userToken.Uid, request.GoalValue)
		if err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// Investment operations
func (dc *controller) CreateInvestment() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var request CreateInvestmentRequest
		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		investment, err := dc.Service.CreateInvestment(ctx, userToken.Uid, request.Name, request.CurrentValue)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, investment)
	}
}

func (dc *controller) FindInvestments() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		investments, err := dc.Service.FindInvestments(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, investments)
	}
}

func (dc *controller) UpdateInvestment() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		id := c.Param("id")

		var request UpdateInvestmentRequest
		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		if err := dc.Service.UpdateInvestment(ctx, userToken.Uid, id, request.Name, request.CurrentValue); err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

func (dc *controller) DeleteInvestment() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		id := c.Param("id")

		if err := dc.Service.DeleteInvestment(ctx, userToken.Uid, id); err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// Asset operations
func (dc *controller) CreateAsset() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var request CreateAssetRequest
		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		asset, err := dc.Service.CreateAsset(ctx, userToken.Uid, request.Name, request.CurrentValue)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, asset)
	}
}

func (dc *controller) FindAssets() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		assets, err := dc.Service.FindAssets(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, assets)
	}
}

func (dc *controller) UpdateAsset() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		id := c.Param("id")

		var request UpdateAssetRequest
		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		if err := dc.Service.UpdateAsset(ctx, userToken.Uid, id, request.Name, request.CurrentValue); err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

func (dc *controller) DeleteAsset() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		id := c.Param("id")

		if err := dc.Service.DeleteAsset(ctx, userToken.Uid, id); err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// Snapshot operations
func (dc *controller) FindNetWorthHistory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get limit from query parameter, default to 12
		limitStr := c.QueryParam("limit")
		limit := 12
		if limitStr != "" {
			if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
				limit = parsedLimit
			}
		}

		snapshots, err := dc.Service.FindNetWorthHistory(ctx, userToken.Uid, limit)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, snapshots)
	}
}

func (dc *controller) FindLatestNetWorthSnapshot() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		snapshot, err := dc.Service.FindLatestNetWorthSnapshot(ctx)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, snapshot)
	}
}

// Helper
func (dc *controller) buildNetWorthHistoryCard(snapshots []*dashboard.NetWorthSnapshot) []*NetWorthHistoryCard {
	var cards []*NetWorthHistoryCard
	// Fill empty month with zero values
	for i := 0; i < 12; i++ {
		card := &NetWorthHistoryCard{
			Stacks: []*NetWorthStack{
				{
					Value: 0,
					Color: "#4CAF50", // Strategic Fund color
				},
				{
					Value: 0,
					Color: "#2196F3", // Investments color
				},
				{
					Value: 0,
					Color: "#FFC107", // Assets color
				},
			},
			Label: time.Now().AddDate(0, -i, 0).Format("Jan"),
		}
		cards = append(cards, card)
	}

	// Add snapshots replacing the ones with zero values
	for i, card := range cards {
		for _, snapshot := range snapshots {
			if snapshot.Date.Format("Jan") == card.Label {
				cards[i].Stacks[0].Value = snapshot.StrategicFundValue
				cards[i].Stacks[1].Value = snapshot.InvestmentsValue
				cards[i].Stacks[2].Value = snapshot.AssetsValue
			}
		}
	}

	return cards
}
